import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormUpload,
  ProFormItem
} from '@ant-design/pro-components';
import { history } from 'umi'
import { Button, Dropdown, Space, message, Upload } from 'antd';
import { UploadImg } from '@/util/upload';

import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { dealParamsTime, getDateTimeStr } from '@/util';


async function getBagList(options) {
  const { data } = await request('/admin/v1.0/luckybag/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}

async function getIPList(options) {
  const res = await request('/admin/v1.0/cardip/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res.data.list;
}

async function getCardList(options) {
  const res = await request('/admin/v1.0/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res.data.list;
}

export async function editBag(options) {
  return request('/admin/v1.0/luckybag/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function addBag(options) {
  return request('/admin/v1.0/luckybag/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function updateBagStatus(options) {
  return request('/admin/v1.0/luckybag/update_status', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function copyBag(options) {
  return request('/admin/v1.0/luckybag/copy', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}


async function getUserDetail(options) {
  // TODO: 获取用户列表
  const res = await request('/admin/v1.0/luckybag/user_stat', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });

  return res;
}

const expandedRowRender = async (data) => {

  return (
    <ProTable
      columns={[
        { title: '用户ID', dataIndex: 'uid', key: 'uid' },
        { title: '用户昵称', dataIndex: 'nickname', key: 'nickname' },
        { title: '下单次数', dataIndex: 'order_count', key: 'order_count' },
        { title: '抽卡次数', dataIndex: 'card_count', key: 'card_count' },
      ]}
      rowKey="uid"
      headerTitle="玩家分布"
      search={false}
      options={false}
      dataSource={{
        data: data.data,
        success: true,
        total: data.data?.length || 0,
      }}
      pagination={false}
    />
  );
};

const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [expandedRowData, setExpandedRowData] = useState({});
  const tableRef = useRef(null);
  const [ipList, setIpList] = useState([]);
  const [cardList, setCardList] = useState([]);
  const [fileList, setFileList] = useState([]);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '名称',
      dataIndex: 'bag_name',
    },
    {
      title: '福袋类型',
      dataIndex: 'bag_type',
      valueEnum: {
        1: { text: '福袋赏', status: 1 },
        2: { text: '聚福赏', status: 2 },
      },
    },
    {
      title: '卡片IP',
      dataIndex: 'card_ip',
    },
    {
      title: '图片',
      dataIndex: 'image',
      valueType: 'image',
    },
    {
      title: '玩家分布',
      dataIndex: 'xx',
      render: (_, rowInfo) => {
        return (
          <a onClick={async () => {
            setExpandedRowKeys(prevKeys => {
              if (prevKeys.includes(rowInfo.id)) {
                return [];
              } else {
                return [rowInfo.id];
              }
            });
            if (!expandedRowKeys.includes(rowInfo.id)) {
              const res = await getUserDetail({ bag_id: rowInfo.id });
              setExpandedRowData(res);
            }
          }}>
            {expandedRowKeys.includes(rowInfo.id) ? '收起' : '展开'}
          </a>
        );
      },
    },
    {
      title: '已售统计',
      dataIndex: 'bag_count',
      search: false,
      render: (_, rowInfo) => {
        return <div>
          <div>
            <span>收入</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>纯收入</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>成本</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>毛利</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>毛利率</span>
            <span>{rowInfo.bag_count}</span>
          </div>
        </div>;
      },
    },

    {
      title: '待售 / 总数 ',
      dataIndex: 'shouru',
      search: false,
      render: (_, rowInfo) => {
        return <div>
          <div>
            <span>第一箱：</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>第二箱：</span>
            <span>{rowInfo.bag_count}</span>
          </div>
          <div>
            <span>第三箱：</span>
            <span>{rowInfo.bag_count}</span>
          </div>
        </div>;
      },
    },
    {
      title: '锁定状态',
      dataIndex: 'openid',
      search: false,
      render: (_, rowInfo) => {
        return <div>
          <div>
            <span>第一箱：</span>
            <span></span>
          </div>
        </div>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        1: { text: '已上架', status: 1 },
        0: { text: '待上架', status: 0 },
      },
    },
    {
      title: '更新时间',
      dataIndex: 'm_time',
      sorter: true,
      render: (_, rowInfo) => {
        return getDateTimeStr(rowInfo.m_time);
      },
    },
    // {
    //   title: '开奖时间',
    //   dataIndex: 'c_time',
    //   hideInTable: true,
    //   valueType: 'dateRange',
    // },
    // {
    //   title: '开奖状态',
    //   dataIndex: 'c_time',
    // },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },
          {
            type: 'divider',
          },
          {
            key: '2',
            label: rowInfo.status === 1 ? '下架' : '上架',
          },
          {
            key: '3',
            label: '锁定福袋',
          },
          {
            key: '4',
            label: '复制福袋',
          },
          {
            key: '5',
            label: '编辑卡片',
          },
        ];
        if (rowInfo.status === 1) {
          menuItems.splice(5, 1);
        }

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  getIPList().then(res => {
                    setIpList(res.map(item => ({
                      label: item.card_ip_name,
                      value: item.id,
                    })));
                  });
                  getCardList().then(res => {
                    setCardList(res.map(item => ({
                      label: `${item.id} - ${item.card_name}(${item.stock})-(${item.card_sold_price})`,
                      value: item.id,
                    })));
                  });
                  setEditVisible(true);
                  setEditData(rowInfo);
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: rowInfo.image,
                  }]);
                }
                // 上架/下架
                if (item.key === '2') {
                  updateBagStatus({
                    id: rowInfo.id,
                    status: rowInfo.status === 1 ? 0 : 1,
                  });
                  tableRef.current.reload();
                }
                // 复制
                if (item.key === '4') {
                  copyBag({
                    id: rowInfo.id,
                  }).then(res => {
                    if (res.code === 0) {
                      message.success('复制成功');
                      tableRef.current.reload();
                    }
                  });

                }

                // 编辑卡片
                if (item.key === '5') {
                  history.push(`/goods/bag/detail/${rowInfo.id}/${rowInfo.pkg_sold_info[0].bag_package_id}`);
                }

              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={tableRef}
        rowKey="id"
        request={getBagList}
        columns={columns}
        options={false}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}
        expandable={{
          showExpandColumn: false,
          expandedRowKeys: expandedRowKeys,
          expandedRowRender: () => expandedRowRender(expandedRowData),
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              getIPList().then(res => {
                setIpList(res.map(item => ({
                  label: item.card_ip_name,
                  value: item.id,
                })));
              });
              getCardList().then(res => {
                setCardList(res.map(item => ({
                  label: `${item.id} - ${item.card_name}(${item.stock})-(${item.card_sold_price})`,
                  value: item.id,
                })));
              });
              setAddVisible(true);
              setFileList([]);
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />


      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={editData}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          editBag({ ...value, id: editData?.id, image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('编辑成功');
              tableRef.current.reload();
              setEditVisible(false);

            }
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="bag_name" label="名称" width="md" />
        <ProFormSelect name="bag_type" label="福袋类型" width="md" options={[
          {
            value: 1,
            label: '福袋赏',
          },
          // {
          //   value: '2',
          //   label: '聚福赏',
          // },
        ]} />
        <ProFormSelect name="last_reward" label="Last赏" width="md" options={cardList} />
        <ProFormSelect name="card_ip" label="卡牌IP" width="md" options={ipList} />
        <ProFormItem label="图片" name="image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>
        </ProFormItem>
        <ProFormDigit name="bag_price" label="单价" width="md" />
        <ProFormDigit name="bag_count" label="总数" width="md" />
        <ProFormDigit name="bag_package_count" label="箱数" width="md" max={6} min={1} />
        <ProFormDigit name="sort" label="排序" width="md" />
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addBag({ ...value, card_ip: value.card_ip, image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('新增成功');
              setAddVisible(false);
              tableRef.current.reload();
            }
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="bag_name" label="名称" width="md" />
        <ProFormSelect name="bag_type" label="福袋类型" width="md" options={[
          {
            value: 1,
            label: '福袋赏',
          },
          // {
          //   value: '2',
          //   label: '类福赏',
          // },
        ]} />
        <ProFormSelect name="last_reward" label="Last赏" width="md" options={cardList} />
        <ProFormSelect name="card_ip" label="卡牌IP" width="md" options={ipList} />
        <ProFormItem label="图片" name="image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>
        </ProFormItem>
        <ProFormDigit name="bag_price" label="单价" width="md" />
        <ProFormDigit name="bag_count" label="总数" width="md" />
        <ProFormDigit name="bag_package_count" label="箱数" width="md" max={6} min={1} />
        <ProFormDigit name="sort" label="排序" width="md" />
      </ModalForm>
    </PageContainer>
  );
};

export default TableList;

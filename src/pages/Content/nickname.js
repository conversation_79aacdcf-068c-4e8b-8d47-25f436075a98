import { DownOutlined, PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, message, Modal } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { getDateTimeStr } from '@/util';

async function getData(options) {
  // TODO: 获取用户列表
  const res = await request('/admin/v1.0/nickname/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: res.data.list,
    success: true,
    total: res.data.total,
  };
}

export async function addNickname(options) {
  return request('/admin/v1.0/nickname/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editNickname(options) {
  return request('/admin/v1.0/nickname/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function deleteNickname(options) {
  return request('/admin/v1.0/nickname/delete', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });
}

const TableList = () => {
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const [addVisible, setAddVisible] = useState(false);
  const tableRef = useRef();

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '修饰词',
      dataIndex: 'nickname_prefix',
    },

    {
      title: '宝可梦',
      dataIndex: 'nickname_suffix',
    },
    {
      title: '创建时间',
      dataIndex: 'c_time',
      search: false,
      render: (text) => {
        return getDateTimeStr(text);
      }
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },
          {
            key: '2',
            label: '删除',
          }
        ];

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  setEditVisible(true);
                  setEditData(rowInfo)
                }
                // 删除
                if (item.key === '2') {
                  Modal.confirm({
                    title: `确定删除吗?`,
                    icon: <ExclamationCircleFilled />,
                    okText: '确定',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk() {
                      deleteNickname({ id: rowInfo.id }).then(() => {
                        message.success('删除成功');
                        tableRef.current.reload();
                      });
                    },
                    onCancel() {
                      console.log('取消');
                    },
                  });
                }

              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        actionRef={tableRef}
        options={false}
        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}

        toolBarRender={() => [
          <Button key="export" onClick={() => {
            console.log('导入');
          }}>
            导入
          </Button>,
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => setAddVisible(true)}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />
      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={editData}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editNickname({ ...value, id: editData.id }).then(() => {
            message.success('编辑成功');
            setEditVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="nickname_prefix" label="修饰词" width="md" />
        <ProFormText name="nickname_suffix" label="宝可梦" width="md" />
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addNickname({ ...value, id: editData.id }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="nickname_prefix" label="修饰词" width="md" />
        <ProFormText name="nickname_suffix" label="宝可梦" width="md" />
      </ModalForm>

    </PageContainer>
  );
};

export default TableList;

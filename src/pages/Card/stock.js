import { DownOutlined, PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormItem
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, InputNumber, DatePicker, message, Upload } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { UploadImg } from '@/util/upload';
import { dealParamsTime, getDateTimeStr } from '@/util';



async function getData(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}

export async function addStock(options) {
  return request('/admin/v1.0/card/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function editStock(options) {
  return request('/admin/v1.0/card/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}


const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const tableRef = useRef(null);
  const [fileList, setFileList] = useState([]);



  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '名称',
      dataIndex: 'card_name',
    },
    {
      title: '介绍',
      dataIndex: 'description',
      hideInTable: true,
    },
    {
      title: '品级',
      dataIndex: 'card_level',
      hideInTable: true,
      valueEnum: {
        1: { text: '普通', status: 1 },
        2: { text: '稀有', status: 2 },
        3: { text: '史诗', status: 3 },
        4: { text: '传说', status: 4 },
        5: { text: '神话', status: 5 },
      },
    },
    {
      title: 'warehouse',
      dataIndex: 'type',
      hideInTable: true,
      valueEnum: {
        1: { text: '杭州卡片仓库', status: 1 },
        2: { text: '滨江卡片仓库', status: 2 },
      },
    },
    {
      title: '成本价',
      dataIndex: 'card_price',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '价格',
      dataIndex: 'card_sold_price',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '可用库存',
      dataIndex: 'stock',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '已锁库存',
      dataIndex: 'username',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '更新时间',
      dataIndex: 'm_time',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        return <DatePicker.RangePicker />;
      },
    },
    {
      title: '图片',
      dataIndex: 'card_image',
      valueType: 'image',
      search: false,
    },

    {
      title: '售价 / 成本 ',
      dataIndex: 'shouru',
      search: false,
      render: (_, rowInfo) => {
        return `${rowInfo.card_price} / ${rowInfo.card_sold_price}`;
      },
    },
    {
      title: '可用库存',
      dataIndex: 'stock',
      search: false,
    },
    {
      title: '已锁库存',
      dataIndex: 'jinbi',
      search: false,
    },
    {
      title: '统计',
      dataIndex: 'zjxd',
      search: false,
      render: (_, rowInfo) => {
        return <div>
          <div>
            <span>未提货：</span>
            <span>{rowInfo.zjxd}</span>
          </div>
          <div>
            <span>已提货：</span>
            <span>{rowInfo.linghua}</span>
          </div>

        </div>;
      },
    },

    {
      title: '预售',
      dataIndex: 'is_pre_sale',
      search: false,
      render: (_, rowInfo) => {
        return rowInfo.is_pre_sale === 1 ? '是' : '否';
      },
    },

    {
      title: '状态',
      dataIndex: 'status',
      search: false,
      valueEnum: {
        1: { text: '可用', status: 1 },
        0: { text: '不可用', status: 0 },
      },
      // render: (_, record) => {
      //   const { isReceived } = record;
      //   return isReceived ? '是' : '否';
      // },
      // render: (text) => {
      //   return (
      //     <Paragraph ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}>{text}</Paragraph>
      //   )
      // },
      // renderFormItem: (item, config, form) => {
      //   return <ChooseStarSearch item={item} config={config} form={form} />;
      // }
    },
    {
      title: '所属仓库',
      dataIndex: 'warehouse',
      search: false,
      valueEnum: {
        1: { text: '杭州卡片仓库', status: 1 },
        2: { text: '萧山商品仓库', status: 2 },
      },

    },
    {
      title: '创建时间',
      dataIndex: 'c_time',
      search: false,
      render: (_, rowInfo) => {
        return getDateTimeStr(rowInfo.c_time);
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        return (
          <Button type="link" onClick={() => {
            setEditData(rowInfo);
            setEditVisible(true);
            setFileList([{
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: rowInfo.card_image,
            }]);
          }}>编辑</Button>
        );
      },
    },
  ];

  const menuProps = {
    items: [
      {
        label: '全部',
        key: 'all',
      },
      {
        label: '当前页',
        key: 'current',
      },
      {
        label: '选择的行',
        key: 'export',
      },
    ],
    onClick: (e) => {
      console.log(e);
    },
  };

  return (
    <PageContainer>
      <ProTable
        actionRef={tableRef}
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}

        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
          >
            卡片库存导入
          </Button>,
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setAddVisible(true);
              setFileList([]);
            }}
            type="primary"
          >
            新建
          </Button>,
          <Dropdown key="export" menu={menuProps}>
            <Button>
              <DownloadOutlined />
              <Space>
                导出
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />

      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={editData}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editStock({ ...value, id: editData.id, card_image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('编辑成功');
              setEditVisible(false);
              tableRef.current.reload();
            }
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="id" width="md" readonly />
        {/* <ProFormText name="phone" label="图鉴编号" width="md" /> */}
        <ProFormText name="card_name" label="名称" width="md" />
        <ProFormItem label="图片" name="card_image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormText name="description" label="介绍" width="md" />

        <ProFormRadio.Group
          name="card_level"
          label="品级"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '普通',
              value: 1,
            },
            {
              label: '稀有',
              value: 0,
            },
            {
              label: '史诗',
              value: 2,
            },
            {
              label: '传说',
              value: 3,
            },
            {
              label: '神话',
              value: 4,
            },
          ]}
        />
        <ProFormDigit name="card_price" label="成本价" width="md" />
        <ProFormDigit name="card_sold_price" label="价格" width="md" />
        <ProFormDigit name="stock" label="可用库存" width="md" />
        <ProFormRadio.Group
          name="status"
          label="状态"
          options={[
            {
              label: '可用',
              value: 1
            },
            {
              label: '不可用',
              value: 0,
            },
          ]}
        />
        <ProFormRadio.Group
          name="is_pre_sale"
          label="是否预售"
          options={[
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0
            },
          ]}
        />
        <ProFormRadio.Group
          name="is_default"
          label="福袋默认卡片"
          options={[

            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ]}
        />
        <ProFormRadio.Group
          name="warehouse"
          label="所属仓库"
          initialValue={1}
          options={[
            {
              label: '杭州卡片仓库',
              value: 1,
            },
            {
              label: '萧山商品仓库',
              value: 2,
            },
          ]}
        />

        <ProFormRadio.Group
          name="label"
          label="标识"
          extra="不参与附赠商品,勿填写'标识'"
          options={[
            {
              label: '特定（包）',
              value: 1,
            },
            {
              label: '特定（盒）',
              value: 2,
            },
            {
              label: '赠品',
              value: 3,
            },
          ]}
        />
        {/* <ProFormDatePicker name="c_time" label="创建时间" width="md" format='YYYY-MM-DD HH:mm:ss' />
        <ProFormDatePicker name="m_time" label="更新时间" width="md" format='YYYY-MM-DD HH:mm:ss' /> */}
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addStock({ ...value, card_image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('新增成功');
              setAddVisible(false);
              tableRef.current.reload();
            }
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        {/* <ProFormText name="uid" label="商品编号" width="md" /> */}
        {/* <ProFormText name="phone" label="图鉴编号" width="md" /> */}
        <ProFormText name="card_name" label="名称" width="md" />
        <ProFormItem label="图片" name="card_image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormText name="description" label="介绍" width="md" />

        <ProFormRadio.Group
          name="card_level"
          label="品级"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '普通',
              value: 1,
            },
            {
              label: '稀有',
              value: 0,
            },
            {
              label: '史诗',
              value: 2,
            },
            {
              label: '传说',
              value: 3,
            },
            {
              label: '神话',
              value: 4,
            },
          ]}
        />
        <ProFormDigit name="card_price" label="成本价" width="md" />
        <ProFormDigit name="card_sold_price" label="价格" width="md" />
        <ProFormDigit name="stock" label="可用库存" width="md" />
        <ProFormRadio.Group
          name="status"
          label="状态"
          options={[
            {
              label: '可用',
              value: 1,
            },
            {
              label: '不可用',
              value: 0,
            },
          ]}
        />
        <ProFormRadio.Group
          name="is_pre_sale"
          label="是否预售"
          options={[
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ]}
        />
        <ProFormRadio.Group
          name="is_default"
          label="福袋默认卡片"
          options={[

            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ]}
        />
        <ProFormRadio.Group
          name="warehouse"
          label="所属仓库"
          initialValue={1}
          options={[
            {
              label: '杭州卡片仓库',
              value: 1,
            },
            {
              label: '萧山商品仓库',
              value: 2,
            },
          ]}
        />

        <ProFormRadio.Group
          name="label"
          label="标识"
          extra="不参与附赠商品,勿填写'标识'"
          options={[
            {
              label: '特定（包）',
              value: 1,
            },
            {
              label: '特定（盒）',
              value: 2,
            },
            {
              label: '赠品',
              value: 3,
            },
          ]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default TableList;

import { DownloadOutlined, DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormItem,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, DatePicker, InputNumber, Upload, message } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { UploadImg } from '@/util/upload';
import { getDateTimeStr } from '@/util';

async function getData(options) {
  // TODO: 获取用户列表
  const res = await request('/admin/v1.0/banner/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: res.data.list,
    success: true,
    total: res.data.total,
  };
}

export async function addBanner(options) {
  return request('/admin/v1.0/banner/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editBanner(options) {
  return request('/admin/v1.0/banner/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}


const TableList = () => {
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const [addVisible, setAddVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const tableRef = useRef();
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '名称',
      dataIndex: 'title',
    },
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: {
        1: { text: '首页', status: 1 },
        2: { text: 'banner', status: 2 },
      },
    },
    {
      title: '图片',
      dataIndex: 'image',
      valueType: 'image',
    },
    {
      title: '跳转',
      dataIndex: 'jumpType',
      valueEnum: {
        1: { text: '仅展示', status: 0 },
        2: { text: '福袋详情', status: 1 },
        3: { text: '抽卡机详情', status: 2 },
        4: { text: 'H5链接', status: 3 },
        5: { text: '抽奖活动详情', status: 4 },
      },
    },
    {
      title: '跳转链接',
      dataIndex: 'jumpUrl',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '已关闭', status: 0 },
        1: { text: '已启用', status: 1 },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'cTime',
      render: (text) => {
        return getDateTimeStr(text);
      }
    },
    {
      title: '更新时间',
      dataIndex: 'mTime',
      sorter: true,
      render: (text) => {
        return getDateTimeStr(text);
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (text, record) => {
        return (
          <Space>
            <Button type="link" onClick={() => {
              setEditVisible(true);
              setEditData(record);
              setFileList([{
                uid: '-1',
                name: 'image.png',
                status: 'done',
                url: record.image,
              }]);
            }}>编辑</Button>
          </Space>
        )
      },
    },

  ];


  const JUMP_WAY_OPTIONS = [
    {
      label: '仅展示',
      value: 0,
    },
    {
      label: '福袋详情',
      value: 1,
    },
    {
      label: '第三方小程序',
      value: 3,
    },
    {
      label: '抽卡机详情',
      value: 4,
    },
    {
      label: 'H5链接',
      value: 5,
    },
    {
      label: '抽奖活动详情',
      value: 5,
    },
  ];



  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        actionRef={tableRef}
        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={false}
        toolBarRender={() => [
          <Button key="export" onClick={() => {
            setAddVisible(true);
            setFileList([]);
          }}>
            新增
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />

      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={editData}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          // 编辑
          editBanner({ ...value, id: editData.id, image: fileList[0].url }).then(() => {
            message.success('编辑成功');
            setEditVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormRadio.Group
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              label: '首页',
              value: 1,
            },
            {
              label: 'banner',
              value: 2,
            }
          ]}
        />
        <ProFormText name="id" label="ID" width="md" readonly />
        <ProFormText name="title" label="名称" width="md" />
        <ProFormItem label="图片" name="image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }
          </Upload>

        </ProFormItem>
        <ProFormSelect
          label="跳转类型"
          name="jumpType"
          options={JUMP_WAY_OPTIONS}
          placeholder="请选择跳转类型"
          width="md"
        />
        <ProFormText name="jumpUrl" label="跳转链接" width="md" />

        <ProFormRadio.Group
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '已关闭',
              value: 0,
            },
            {
              label: '已启用',
              value: 1,
            }
          ]}
        />
        <ProFormDigit name="sort" label="排序" extra="排序越大越靠前" width="md" />
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addBanner({ ...value, image: fileList[0].url }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormRadio.Group
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              label: '首页',
              value: 1,
            },
            {
              label: 'banner',
              value: 2,
            }
          ]}
        />
        <ProFormText name="title" label="名称" width="md" />
        <ProFormItem label="图片" name="image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormSelect
          label="跳转类型"
          name="jumpType"
          options={JUMP_WAY_OPTIONS}
          placeholder="请选择跳转类型"
          width="md"
        />
        <ProFormText name="jumpUrl" label="跳转链接" width="md" />

        <ProFormRadio.Group
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '已关闭',
              value: 0,
            },
            {
              label: '已启用',
              value: 1,
            }
          ]}
        />
        <ProFormDigit name="sort" label="排序" extra="排序越大越靠前" width="md" />
      </ModalForm>

    </PageContainer>
  );
};

export default TableList;

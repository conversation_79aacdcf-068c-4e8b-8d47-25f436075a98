import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';
import { PlusOutlined, DoubleRightOutlined, DoubleLeftOutlined, EditOutlined, MoreOutlined, BarsOutlined } from '@ant-design/icons';

import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
  ProFormSelect,
  ProFormUploadDragger,
  ProFormDigit,
  EditableProTable,
  ProCard,
  ProFormField,

} from '@ant-design/pro-components';
import { useParams, history } from '@umijs/max';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Drawer, Input, message, Dropdown, Space, Descriptions } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { request } from '@umijs/max';
// import { editBag } from './Bag';


async function getData(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/luckybag/package/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });


  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}


async function getBagDetail(options) {
  const res = await request('/admin/v1.0/luckybag/get', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });
  return res;
}

async function add(options) {
  return request('/admin/v1.0/luckybag/package/card/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function edit(options) {
  return request('/admin/v1.0/luckybag/package/card/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function getCardList(options) {
  const res = await request('/admin/v1.0/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res.data.list;
}



const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '福袋名称',
    dataIndex: 'name',
    editable: false,
  },
  {
    title: '赏品信息',
    dataIndex: 'rewardInfo',
    width: '40%',
  },
  {
    title: '售价',
    dataIndex: 'price',
    sorter: true,
  },
  {
    title: '成本',
    dataIndex: 'acost',
    sorter: true,
  },
  {
    title: '数量',
    dataIndex: 'number',
  },
  {
    title: '已售',
    dataIndex: 'sold',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    valueType: 'dateRange',
  },
  {
    title: '更新时间',
    dataIndex: 'u_time',
    valueType: 'dateRange',
    sorter: true,
  },
  {
    title: '操作',
    valueType: 'option',
    width: 150,
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id);
        }}
      >
        编辑
      </a>,
    ],
  },
];

const BagDetail = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editableKeys, setEditableRowKeys] = useState([]);
  const [bagDetail, setBagDetail] = useState({});
  const [cardList, setCardList] = useState([]);
  const [currentPackageIndex, setCurrentPackageIndex] = useState(0);
  const [packages, setPackages] = useState([]);
  const tableRef = useRef();
  const params = useParams();

  useEffect(() => {
    getBagDetail({ id: Number(params.id) }).then(res => {
      setBagDetail(res.data);
      if (res.data.pkg_sold_info) {
        setPackages(res.data.pkg_sold_info);
        // 根据URL参数设置当前箱子
        const currentPkgId = Number(params.bag_id);
        const pkgIndex = res.data.pkg_sold_info.findIndex(pkg => pkg.bag_package_id === currentPkgId);
        setCurrentPackageIndex(pkgIndex >= 0 ? pkgIndex : 0);
      }
    });
  }, [params.id, params.bag_id]);

  const currentPackage = packages[currentPackageIndex];

  const handlePrevPackage = () => {
    if (currentPackageIndex > 0) {
      const newIndex = currentPackageIndex - 1;
      setCurrentPackageIndex(newIndex);
      history.replace(`/goods/bag/detail/${params.id}/${packages[newIndex].bag_package_id}`);
    }
  };

  const handleNextPackage = () => {
    if (currentPackageIndex < packages.length - 1) {
      const newIndex = currentPackageIndex + 1;
      setCurrentPackageIndex(newIndex);
      history.replace(`/goods/bag/detail/${params.id}/${packages[newIndex].bag_package_id}`);
    }
  };

  return (
    <PageContainer
      title={`${bagDetail.bag_name} - ${currentPackage?.package_name || `第${currentPackageIndex + 1}箱`}`}
      content={
        <Descriptions column={3}>
          <Descriptions.Item label="福袋单价">¥{bagDetail.bag_price}</Descriptions.Item>
          <Descriptions.Item label="福袋总数量">{bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="福袋总价">¥{(bagDetail.bag_price * bagDetail.bag_count).toFixed(2)}</Descriptions.Item>
          <Descriptions.Item label="当前箱总数">{currentPackage?.total_count || 0}</Descriptions.Item>
          <Descriptions.Item label="当前箱已售">{currentPackage?.sold_count || 0}</Descriptions.Item>
          <Descriptions.Item label="当前箱剩余">{currentPackage?.remaining_count || 0}</Descriptions.Item>
        </Descriptions>
      }
      header={{
        extra: [
          <Button
            key="prev"
            type="link"
            disabled={currentPackageIndex === 0}
            onClick={handlePrevPackage}
          >
            <DoubleLeftOutlined />
            上一箱
          </Button>,
          <span key="pagination" style={{ margin: '0 16px' }}>
            {currentPackageIndex + 1} / {packages.length}
          </span>,
          <Button
            key="next"
            type="link"
            disabled={currentPackageIndex === packages.length - 1}
            onClick={handleNextPackage}
          >
            下一箱
            <DoubleRightOutlined />
          </Button>
        ]
      }}>

      <EditableProTable
        actionRef={tableRef}
        headerTitle="xxx"
        columns={columns}
        rowKey="id"
        request={getData}
        params={{
          bag_package_id: currentPackage?.bag_package_id || Number(params.bag_id),
          bag_id: Number(params.id),
        }}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              key="save"
              onClick={() => {
                setAddVisible(true);
                getCardList().then(res => {
                  setCardList(res.map(item => ({
                    label: `${item.id} - ${item.card_name}(${item.stock})-(${item.card_sold_price})`,
                    value: item.id,
                  })));
                });

              }}
            >
              新增
            </Button>,
          ];
        }}

        editable={{
          // form,
          // type: 'multiple',
          editableKeys,
          showAddRow: false,
          onSave: async (_, rowInfo) => {
            return edit(rowInfo);
          },
          onChange: setEditableRowKeys,
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}

      />
      <ModalForm
        title="新增"
        layout="horizontal"
        open={addVisible}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={async (value) => {
          add({
            ...value,
            bag_id: Number(params.id),
            bag_package_id: currentPackage?.bag_package_id || Number(params.bag_id)
          }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect name="card_id" label="卡片" width="md" options={cardList} />
        <ProFormDigit
          name="card_num"
          label="数量"
          min={1}          // 最小值
          max={100}        // 最大值
          step={1}           // 步长
          defaultValue={1} // 默认值
          rules={[{ required: true, message: '请选择数量' }]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default BagDetail;

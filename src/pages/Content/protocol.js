import { DownOutlined, PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import {
  <PERSON>dalForm,
  PageContainer,
  ProFormSelect,
  ProTable,
  ProForm
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, message, Modal } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';

import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

async function getData(options) {
  const res = await request('/admin/v1.0/aggrement/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: res.data,
    success: true,
    total: res.data.length,
  };
}


export async function addProtocol(options) {
  return request('/admin/v1.0/aggrement/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editProtocol(options) {
  return request('/admin/v1.0/aggrement/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function deleteProtocol(options) {
  return request('/admin/v1.0/aggrement/delete', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });
}

const TableList = () => {
  const [editVisible, setEditVisible] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [contentValue, setContentValue] = useState('');
  const [editData, setEditData] = useState({});
  const actionRef = useRef();

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '内容',
      dataIndex: 'content',
      ellipsis: true,
    },

    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: {
        1: { text: '用户协议', status: '1' },
        2: { text: '隐私协议', status: '2' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'c_time',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '更新时间',
      dataIndex: 'm_time',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },

          {
            key: '2',
            label: '删除',
          },

        ];

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  setEditVisible(true);
                  setEditData(rowInfo)
                  setContentValue(rowInfo.content || '')
                }
                // 删除
                if (item.key === '2') {
                  Modal.confirm({
                    title: `确定删除吗?`,
                    icon: <ExclamationCircleFilled />,
                    okText: '确定',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk() {
                      deleteProtocol({ id: rowInfo.id }).then(() => {
                        message.success('删除成功');
                        actionRef.current.reload();
                      });
                    },
                    onCancel() {
                      console.log('取消');
                    },
                  });
                }

              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];


  const TYPE_OPTIONS = [
    {
      label: '用户协议',
      value: 1,
    },
    {
      label: '隐私政策',
      value: 2,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        search={false}
        actionRef={actionRef}
        toolBarRender={() => [
          <Button key="export" onClick={() => {
            console.log('导入');
          }}>
            导入
          </Button>,
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setAddVisible(true)
              setContentValue('')
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />
      <ModalForm
        layout="horizontal"
        open={editVisible}
        initialValues={editData}
        title="编辑"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editProtocol({
            ...value,
            id: editData.id,
            content: contentValue.replace(/<[^>]*>?/g, ''),
          }).then(() => {
            message.success('编辑成功');
            setEditVisible(false);
            actionRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProForm.Item
          label="内容"
          name="content"
          // initialValue={defaultData}
          trigger="onValuesChange"
        >
          <ReactQuill theme="snow" value={contentValue} onChange={setContentValue} />
        </ProForm.Item>
        {/* <ProFormTextArea name="content" label="内容" /> */}
        <ProFormSelect name="type" label="类型" options={TYPE_OPTIONS} width="md" />

      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addProtocol({
            ...value,
            content: contentValue.replace(/<[^>]*>?/g, ''),
          }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            actionRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProForm.Item
          label="内容"
          name="content"
          // initialValue={defaultData}
          trigger="onValuesChange"
        >
          <ReactQuill theme="snow" value={contentValue} onChange={setContentValue} />
        </ProForm.Item>
        {/* <ProFormTextArea name="content" label="内容" /> */}
        <ProFormSelect name="type" label="类型" options={TYPE_OPTIONS} width="md" />

      </ModalForm>

    </PageContainer>
  );
};

export default TableList;

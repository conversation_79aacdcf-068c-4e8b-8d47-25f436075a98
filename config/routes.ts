﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
  {
    path: '/login',
    layout: false,
    name: 'login',
    component: './Login',

  },
  {
    path: '/home',
    name: '仪表盘',
    // icon: 'smile',
    component: './Home',
  },
  {
    path: '/user',
    name: '用户管理',
    // icon: 'table',
    component: './User',
  },
  {
    path: '/content',
    name: '内容管理',
    // icon: 'crown',
    // access: 'canContent',
    routes: [
      {
        path: '/content',
        redirect: '/content/banner',
      },
      {
        path: '/content/banner',
        name: '轮播管理',
        component: './Content/banner',
      },
      {
        path: '/content/qrcode',
        name: '二维码管理',
        component: './Content/qrcode',
      },
      {
        path: '/content/protocol',
        name: '协议管理',
        component: './Content/protocol',
      },
      {
        path: '/content/nickname',
        name: '默认昵称',
        component: './Content/nickname',
      },
    ],
  },
  {
    path: '/goods',
    name: '商品管理',
    // icon: 'crown',
    // access: 'canGoods',
    routes: [
      {
        path: '/goods',
        redirect: '/goods/bag',
      },
      {
        path: '/goods/bag',
        name: '福袋',
        component: './Goods/Bag',
      },
      // {
      //   path: '/goods/card-drawing',
      //   name: '抽卡机',
      //   component: './Goods',
      // },
      {
        path: '/goods/cardIP',
        name: '卡片IP',
        component: './Goods/CardIP',
      },
      // {
      //   path: '/goods/bagType',
      //   name: '福袋类型',
      //   component: './Goods/BagType',
      // },
      {
        path: '/goods/bag/detail/:id/:bag_id',
        name: '福袋详情',
        component: './Goods/BagDetail',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/order',
    name: '订单管理',
    // icon: 'crown',
    // access: 'canOrder',
    routes: [
      {
        path: '/order',
        redirect: '/order/bag',
      },
      {
        path: '/order/bag',
        name: '福袋订单',
        component: './Order/bag',
      },
      // {
      //   path: '/order/card-drawing',
      //   name: '抽卡机订单',
      //   component: './Order',
      // },
      {
        path: '/order/pickUp',
        name: '提货订单',
        component: './Order/pickUp',
      },
    ],
  },
  {
    path: '/card',
    name: '卡片管理',
    // icon: 'crown',
    // access: 'canCard',
    routes: [
      {
        path: '/card',
        redirect: '/card/stock',
      },
      {
        path: '/card/stock',
        name: '卡片库存',
        component: './Card/stock',
      },
      {
        path: '/card/gallery',
        name: '卡片图鉴',
        component: './Card/gallery',
        hideInMenu: true
      },
    ],
  },

];

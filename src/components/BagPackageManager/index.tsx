import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Switch, message, Table, Button, Space, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';

interface PackageInfo {
  bag_package_id: number;
  package_name: string;
  package_index: number;
  total_count: number;
  sold_count: number;
  remaining_count: number;
  is_locked: boolean;
}

interface BagPackageManagerProps {
  visible: boolean;
  onCancel: () => void;
  bagId: number;
  onSuccess: () => void;
}

const BagPackageManager: React.FC<BagPackageManagerProps> = ({
  visible,
  onCancel,
  bagId,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [packages, setPackages] = useState<PackageInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingPackage, setEditingPackage] = useState<PackageInfo | null>(null);
  const [packageModalVisible, setPackageModalVisible] = useState(false);

  // 获取箱子列表
  const fetchPackages = async () => {
    setLoading(true);
    try {
      const res = await request('/admin/v1.0/luckybag/package/list', {
        method: 'POST',
        data: { bag_id: bagId },
      });
      if (res.code === 0) {
        setPackages(res.data.list || []);
      }
    } catch (error) {
      message.error('获取箱子列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && bagId) {
      fetchPackages();
    }
  }, [visible, bagId]);

  // 添加/编辑箱子
  const handleSavePackage = async (values: any) => {
    try {
      const apiUrl = editingPackage 
        ? '/admin/v1.0/luckybag/package/update'
        : '/admin/v1.0/luckybag/package/create';
      
      const data = editingPackage 
        ? { ...values, bag_package_id: editingPackage.bag_package_id }
        : { ...values, bag_id: bagId };

      const res = await request(apiUrl, {
        method: 'POST',
        data,
      });

      if (res.code === 0) {
        message.success(editingPackage ? '更新成功' : '添加成功');
        setPackageModalVisible(false);
        setEditingPackage(null);
        form.resetFields();
        fetchPackages();
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 删除箱子
  const handleDeletePackage = async (packageId: number) => {
    try {
      const res = await request('/admin/v1.0/luckybag/package/delete', {
        method: 'POST',
        data: { bag_package_id: packageId },
      });

      if (res.code === 0) {
        message.success('删除成功');
        fetchPackages();
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 锁定/解锁箱子
  const handleToggleLock = async (packageId: number, isLocked: boolean) => {
    try {
      const res = await request('/admin/v1.0/luckybag/package/lock', {
        method: 'POST',
        data: { bag_package_id: packageId, is_locked: !isLocked },
      });

      if (res.code === 0) {
        message.success(isLocked ? '解锁成功' : '锁定成功');
        fetchPackages();
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const columns = [
    {
      title: '箱子序号',
      dataIndex: 'package_index',
      width: 100,
    },
    {
      title: '箱子名称',
      dataIndex: 'package_name',
    },
    {
      title: '总数量',
      dataIndex: 'total_count',
      width: 100,
    },
    {
      title: '已售',
      dataIndex: 'sold_count',
      width: 80,
    },
    {
      title: '剩余',
      dataIndex: 'remaining_count',
      width: 80,
      render: (value: number) => (
        <span style={{ color: value > 0 ? '#52c41a' : '#ff4d4f' }}>
          {value}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_locked',
      width: 80,
      render: (isLocked: boolean) => (
        <span style={{ color: isLocked ? '#ff4d4f' : '#52c41a' }}>
          {isLocked ? '已锁定' : '正常'}
        </span>
      ),
    },
    {
      title: '操作',
      width: 200,
      render: (_, record: PackageInfo) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingPackage(record);
              form.setFieldsValue(record);
              setPackageModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleToggleLock(record.bag_package_id, record.is_locked)}
          >
            {record.is_locked ? '解锁' : '锁定'}
          </Button>
          <Popconfirm
            title="确定删除这个箱子吗？"
            onConfirm={() => handleDeletePackage(record.bag_package_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="箱子管理"
        open={visible}
        onCancel={onCancel}
        width={800}
        footer={[
          <Button key="cancel" onClick={onCancel}>
            关闭
          </Button>,
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingPackage(null);
              form.resetFields();
              setPackageModalVisible(true);
            }}
          >
            添加箱子
          </Button>
        </div>
        
        <Table
          columns={columns}
          dataSource={packages}
          rowKey="bag_package_id"
          loading={loading}
          pagination={false}
          size="small"
        />
      </Modal>

      <Modal
        title={editingPackage ? '编辑箱子' : '添加箱子'}
        open={packageModalVisible}
        onCancel={() => {
          setPackageModalVisible(false);
          setEditingPackage(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePackage}
        >
          <Form.Item
            name="package_name"
            label="箱子名称"
            rules={[{ required: true, message: '请输入箱子名称' }]}
          >
            <Input placeholder="请输入箱子名称" />
          </Form.Item>
          
          <Form.Item
            name="package_index"
            label="箱子序号"
            rules={[{ required: true, message: '请输入箱子序号' }]}
          >
            <InputNumber
              min={1}
              max={10}
              placeholder="请输入箱子序号"
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="total_count"
            label="总数量"
            rules={[{ required: true, message: '请输入总数量' }]}
          >
            <InputNumber
              min={1}
              placeholder="请输入总数量"
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="is_locked"
            label="是否锁定"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default BagPackageManager;

import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormUpload,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormItem,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, Image, message, Upload, Avatar } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { dealParamsTime, getDateTimeStr } from '@/util';
import { UploadImg } from '@/util/upload';

async function getUser(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/user/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}

export async function editUser(options) {
  return request('/admin/v1.0/user/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function addUser(options) {
  return request('/admin/v1.0/user/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function forbiddenUser(options) {
  const params = {
    uid: options.uid,
    status: Number(options.status),
    forbidden_end_time: options.forbidden_end_time ? dealParamsTime(options.forbidden_end_time) : '',
  };
  return request('/admin/v1.0/user/forbidden', {
    method: 'POST',
    data: {
      ...(params || {}),
    },
  });
}

export async function cancelForbiddenUser(options) {
  const params = {
    uid: options.uid,
    status: 1,
  };
  return request('/admin/v1.0/user/forbidden', {
    method: 'POST',
    data: {
      ...(params || {}),
    },
  });
}

async function getUserDetail(options) {
  // TODO: 获取用户列表
  const res = await request('/admin/v1.0/user_box/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res
}

const expandedRowRender = async (data) => {
  return (
    <ProTable
      columns={[
        { title: '卡片ID', dataIndex: 'card_id', key: 'card_id' },
        { title: '卡片名称', dataIndex: 'card_name', key: 'card_name' },
        { title: '卡片数量', dataIndex: 'card_num', key: 'card_num' },
        { title: '获得时间', dataIndex: 'last_receive_time', key: 'last_receive_time' },
      ]}
      headerTitle="盒柜明细"
      search={false}
      options={false}
      dataSource={{
        data: data.data?.list || [],
        success: true,
        total: data.data?.total || 0,
      }}
      pagination={false}
    />
  );
};

const TableList = () => {
  const tableRef = useRef();
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const [ffVisible, setFfVisible] = useState(false);
  // const [kcVisible, setKcVisible] = useState(false);
  const [fjVisible, setFjVisible] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [expandedRowData, setExpandedRowData] = useState({});
  const [fileList, setFileList] = useState([]);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '昵称',
      dataIndex: 'username',
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      search: false,
      valueType: 'avatar',
      // render: (_, rowInfo) => {
      //   return <Avatar src={rowInfo.avatar} />
      // }
    },
    {
      title: '盒柜明细',
      dataIndex: 'id',
      search: false,
      render: (_, rowInfo) => {
        return (
          <a onClick={async () => {
            setExpandedRowKeys(prevKeys => {
              if (prevKeys.includes(rowInfo.id)) {
                return [];
              } else {
                return [rowInfo.id];
              }
            });
            if (!expandedRowKeys.includes(rowInfo.id)) {
              const res = await getUserDetail({ uid: rowInfo.id });
              setExpandedRowData(res);
            }
          }}>
            {expandedRowKeys.includes(rowInfo.id) ? '收起' : '展开'}
          </a>
        );
      },
    },
    {
      title: '零花钱',
      dataIndex: 'linghua',
      search: false,
    },
    {
      title: '护符金币',
      dataIndex: 'jinbi',
      search: false,
    },
    {
      title: '最近下单',
      dataIndex: 'zjxd',
      sorter: true,
      search: false,
    },
    {
      title: '收入 / 纯收入 / 卡成本 / 卡数量',
      dataIndex: 'shouru',
      search: false,
      render: (_, rowInfo) => {
        return `${rowInfo.user_stat.user_gmv} / ${rowInfo.user_stat.user_profit} / ${rowInfo.user_stat.user_card_cost} / ${rowInfo.user_stat.user_card_count}`;
      },
    },
    {
      title: 'openid',
      dataIndex: 'openid',
      hideInTable: true,
    },
    {
      title: 'unionid',
      dataIndex: 'unionid',
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        1: { text: '正常', status: 1 },
        0: { text: '账户封禁', status: 0 },
        2: { text: '限制消费', status: 2 },
      },
      render: (_, record) => {
        const { status } = record;
        return status === 0 ? '账户封禁至' + getDateTimeStr(record.forbidden_time) : status === 1 ? '正常' : '限制消费至' + getDateTimeStr(record.forbidden_time);
      },

    },
    {
      title: '注册时间',
      dataIndex: 'zc_time',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'c_time',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },
          {
            type: 'divider',
          },
          {
            key: '2',
            label: '发放管理',
            disabled: true,
          },
          {
            key: '3',
            label: '扣除管理',
            disabled: true,
          },
          {
            key: '4',
            label: '用户封禁',
          },
        ];

        // if (rowInfo.status === '注销') {
        //   menuItems.splice(3, 1);
        // }

        if (rowInfo.status === 0 || rowInfo.status === 2) {
          menuItems.splice(4, 1);
          menuItems.push({
            key: '5',
            label: '取消解封',
          });
        }

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  setEditVisible(true);
                  setEditData(rowInfo);
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: rowInfo.avatar,
                  }]);
                }
                // // 发放管理
                // if (item.key === '2') {
                //   setFfVisible(true);
                // }
                // // 扣除管理
                // if (item.key === '3') {
                //   setKcVisible(true);
                // }
                // 用户封禁
                if (item.key === '4') {
                  setFjVisible(true);
                  setEditData(rowInfo);
                }
                // 取消封禁
                if (item.key === '5') {
                  cancelForbiddenUser({ uid: rowInfo.id });
                  tableRef.current.reload();
                }
              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        actionRef={tableRef}
        request={getUser}
        columns={columns}
        options={false}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}
        expandable={{
          showExpandColumn: false,
          expandedRowKeys: expandedRowKeys,
          expandedRowRender: () => expandedRowRender(expandedRowData),
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setAddVisible(true);
              setFileList([]);
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />
      {/* <ModalForm
        layout="horizontal"
        open={ffVisible}
        title="发放管理"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setFfVisible(false);
          },
        }}
        onFinish={() => {
          // 发放管理
        }}
        initialValues={{}}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect
          width="md"
          options={[
            {
              value: 'chapter',
              label: '盖章后生效',
            },
          ]}
          name="chapter"
          label="选择优惠券"
          placeholder="请选择优惠券"
        />
        <ProFormDigit width="md" name="num" label="优惠券数量" placeholder="请输入优惠券数量" />
        <ProFormMoney
          width="md"
          name="money"
          label="零花钱"
          placeholder="请输入零花钱"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
        <ProFormMoney
          width="md"
          name="money"
          label="代理充值零花钱"
          placeholder="请输入代理充值零花钱"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
        <ProFormMoney
          width="md"
          name="money"
          label="护符金币"
          placeholder="请输入护符金币"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
      </ModalForm> */}
      <ModalForm
        layout="horizontal"
        open={fjVisible}
        title="用户封禁"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setFjVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          forbiddenUser({ ...value, uid: editData.id });
          setFjVisible(false);
          tableRef.current.reload();
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect
          width="md"
          options={[
            {
              value: 0,
              label: '账户封禁',
            },
            {
              value: 1,
              label: '正常',
            },
            {
              value: 2,
              label: '限制消费',
            },
          ]}
          name="status"
          label="封禁类型"
          placeholder="请选择封禁类型"
          rules={[{ required: true, message: '请选择封禁类型' }]}
        />

        <ProFormDatePicker name="forbidden_end_time" label="封禁至" extra="未填时间表示永久封禁" />
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={editData}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editUser({ ...value, avatar: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('编辑成功');
              setEditVisible(false);
              tableRef.current.reload();
            }
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="ID" width="md" readonly />
        {/* <ProFormText name="uid" label="UID" width="md" /> */}
        <ProFormText name="phone" label="手机号" width="md" />
        <ProFormText name="username" label="昵称" width="md" />
        <ProFormItem label="头像" name="avatar">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormText name="openid" label="openid" readonly width="md" />
        <ProFormText name="unionid" label="unionid" readonly width="md" />
        <ProFormText name="device" label="设备信息" readonly width="md" />
        <ProFormRadio.Group
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              value: 0,
              label: '账户封禁',
            },
            {
              value: 1,
              label: '正常',
            },
            {
              value: 2,
              label: '限制消费',
            },
          ]}
        />

        {/* <ProFormText name="c_time" label="创建时间" />
        <ProFormText name="u_time" label="更新时间" /> */}
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          addUser({ ...value, avatar: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('新增成功');
              setAddVisible(false);
              tableRef.current.reload();
            }
          });

        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        {/* <ProFormText name="uid" label="UID" width="md" /> */}
        <ProFormText name="phone" label="手机号" width="md" />
        <ProFormText name="nick_name" label="昵称" width="md" />
        <ProFormItem label="头像" name="avatar">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormRadio.Group
          name="status"
          label="状态"
          initialValue={1}
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              value: 0,
              label: '账户封禁',
            },
            {
              value: 1,
              label: '正常',
            },
            {
              value: 2,
              label: '限制消费',
            },
          ]}
        />
      </ModalForm>
    </PageContainer >
  );
};

export default TableList;

import { DownloadOutlined, DownOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, DatePicker, InputNumber } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';

async function getData(options) {

  const res = await request('/admin/v1.0/user_pickups/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: res.data.list,
    success: true,
    total: res.data.total,
  };
}

export async function editPickUp(options) {
  return request('/admin/v1.0/user_pickups/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editAddress(options) {
  return request('/admin/v1.0/user_pickups/update_address', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}


const TableList = () => {
  const [editVisible, setEditVisible] = useState(false);
  const [editAddressVisible, setEditAddressVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const tableRef = useRef();
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      search: false,
    },
    {
      title: '订单ID',
      dataIndex: 'pickup_no',
      hideInTable: true,
      valueType: 'multiple',
    },
    {
      title: '用户ID',
      dataIndex: 'user_id',
    },
    {
      title: '用户昵称',
      dataIndex: 'user_name',
    },
    {
      title: '赏品名称',
      dataIndex: 'username',
      search: false,
    },
    {
      title: '订单金额',
      dataIndex: 'pickup_money',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 80 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '商品类型',
      dataIndex: 'username',
      hideInTable: true,
      valueEnum: {
        1: { text: '福袋赏', status: 1 },
        2: { text: '聚福赏', status: 2 },
      },
    },
    {
      title: '商品名称',
      dataIndex: 'username',
      hideInTable: true,
    },

    {
      title: '订单金额',
      dataIndex: 'pickup_money',
      search: false,
    },

    {
      title: '抵扣方式',
      dataIndex: 'discount_type',
      valueEnum: {
        1: { text: '满减', status: 1 },
        2: { text: '抵扣', status: 2 },
      },
      search: false,
    },
    {
      title: '抵扣金额',
      dataIndex: 'discount_money',
      search: false,
    },
    {
      title: '支付方式',
      dataIndex: 'pay_type',
      search: false,
      valueEnum: {
        1: { text: '微信', status: '1' },
        2: { text: '支付宝', status: '2' },
      },
    },
    {
      title: '地址',
      dataIndex: 'warehouse',
    },
    {
      title: '物流信息',
      dataIndex: 'jinbi',

    },
    {
      title: '订单编号',
      dataIndex: 'zjxd',
    },
    {
      title: '所属仓库',
      dataIndex: 'warehouse',
      valueEnum: {
        1: { text: '杭州卡片仓库', status: 1 },
        2: { text: '萧山商品库', status: 2 },
      },
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '待发货', status: 0 },
        2: { text: '已发货', status: 1 },
        3: { text: '已收货', status: 3 },
        4: { text: '已取消', status: 4 },
        5: { text: '运费支付失败', status: 5 },

      },
      render: (text) => {
        return (
          <span style={{ color: text === '1' ? '#FF4D4F' : text === '2' ? '#52C41A' : '#F5222D' }}>
            {text === '1' ? '待支付' : text === '2' ? '已支付' : '已取消'}
          </span>
        )
      }
    },

    {
      title: '创建时间',
      dataIndex: 'c_time',
      sorter: true,
      renderFormItem: (item, config, form) => {
        return <DatePicker.RangePicker />;
      },
    },
    {
      title: '更新时间',
      dataIndex: 'm_time',
      sorter: true,
      renderFormItem: (item, config, form) => {
        return <DatePicker.RangePicker />;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (text, record) => {
        return <Space>
          <Button type="link" onClick={() => {
            setEditVisible(true);
            setEditData(record);
          }}>发货</Button>
          <Button type="link" onClick={() => {
            setEditAddressVisible(true);
            setEditData(record);
          }}>修改地址</Button>
        </Space>;
      },
    },

  ];

  const menuProps = {
    items: [
      {
        label: '全部',
        key: 'all',
      },
      {
        label: '当前页',
        key: 'current',
      },
      {
        label: '选择的行',
        key: 'export',
      },
    ],
    onClick: (e) => {
      console.log(e);
    },
  };

  const rowSelection = {
    type: 'checkbox',
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys, selectedRows, 'selectedRowKeys');
    },
  };

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        rowSelection={rowSelection}
        actionRef={tableRef}

        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}
        toolBarRender={() => [
          <Button key="export" onClick={() => {
            console.log('export');
          }}>
            导入物流信息
          </Button>,
          <Dropdown key="export" menu={menuProps}>
            <Button>
              <DownloadOutlined />
              <Space>
                导出
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />

      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="发货"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          // 发货
          editPickUp({ ...value, id: editData.id }).then(() => {
            message.success('发货成功');
            setEditVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="所属仓库" />
        <ProFormText name="uid" label="快递编号" />
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={editAddressVisible}
        title="修改地址"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditAddressVisible(false);
          },
        }}
        onFinish={(value) => {
          // 修改地址
          editAddress({ ...value, id: editData.id }).then(() => {
            message.success('修改地址成功');
            setEditAddressVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="姓名" />
        <ProFormText name="phone" label="电话" />
        <ProFormText name="username" label="地址" />
      </ModalForm>

    </PageContainer>
  );
};

export default TableList;

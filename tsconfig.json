{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "preserve", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"], "@@test/*": ["./src/.umi-test/*"]}}, "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx", "src/pages/User.js", "src/pages/Goods/CardIP.js", "src/pages/Goods/Bag.js", "src/pages/Order/index.js", "src/pages/Order/pickUp.js", "src/pages/Order/bag.js", "src/pages/Goods/BagDetail.js"]}
import { DownloadOutlined, DownOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, DatePicker, InputNumber } from 'antd';
import { useState } from 'react';
import { request } from '@umijs/max';


async function getData(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/luckybag/order/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}

const TableList = () => {

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'user_id',

    },
    {
      title: '用户昵称',
      dataIndex: 'user_nickname',
    },
    {
      title: '金额',
      dataIndex: 'pay_money',
      hideInTable: true,
      renderFormItem: (text) => {
        return <div style={{ display: 'inline-flex', alignItems: 'center' }}>
          <InputNumber
            style={{ width: 85 }}
          // value={this.state.searchTimeRange[0]}
          // onChange={value => this.handleTimeRangeChange('min', value)}
          />
          <span style={{ margin: '0 10px' }}>to</span>
          <InputNumber
            style={{ width: 85 }}
          // value={this.state.searchTimeRange[1]}
          // onChange={value => this.handleTimeRangeChange('max', value)}
          />
        </div>
      },
    },
    {
      title: '商品类型',
      dataIndex: 'username',
      valueEnum: {
        1: { text: '福袋赏', status: '1' },
        2: { text: '聚福赏', status: '2' },
      },
    },
    {
      title: '商品名称',
      dataIndex: 'bag_name',
    },
    {
      title: '福袋箱位',
      dataIndex: 'username',
      search: false,
    },
    {
      title: '赏品名称',
      dataIndex: 'username',
      search: false,
    },
    {
      title: '订单金额',
      dataIndex: 'order_money',
      search: false,
    },

    {
      title: '抵扣方式',
      dataIndex: 'linghua',
      search: false,
    },
    {
      title: '抵扣金额',
      dataIndex: 'linghua',
      search: false,
    },
    {
      title: '支付方式',
      dataIndex: 'jinbi',
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: 'zjxd',
    },

    {
      title: '订单状态',
      dataIndex: 'status',
      valueEnum: {
        1: { text: '待支付', status: '1' },
        2: { text: '已支付', status: '2' },
        3: { text: '已取消', status: '3' },
      },
      render: (text) => {
        return (
          <span style={{ color: text === '1' ? '#FF4D4F' : text === '2' ? '#52C41A' : '#F5222D' }}>
            {text === '1' ? '待支付' : text === '2' ? '已支付' : '已取消'}
          </span>
        )
      }

    },
    {
      title: '创建时间',
      dataIndex: 'zc_time',
      sorter: true,
      renderFormItem: (item, config, form) => {
        return <DatePicker.RangePicker />;
      },
    },
    {
      title: '更新时间',
      dataIndex: 'c_time',
      sorter: true,
      renderFormItem: (item, config, form) => {
        return <DatePicker.RangePicker />;
      },
    },

  ];

  const menuProps = {
    items: [
      {
        label: '全部',
        key: 'all',
      },
      {
        label: '当前页',
        key: 'current',
      },
      {
        label: '选择的行',
        key: 'export',
      },
    ],
    onClick: (e) => {
      console.log(e);
    },
  };

  const rowSelection = {
    type: 'checkbox',
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys, selectedRows, 'selectedRowKeys');
    },
  };

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        rowSelection={rowSelection}

        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}
        toolBarRender={() => [
          <Dropdown key="export" menu={menuProps}>
            <Button>
              <DownloadOutlined />
              <Space>
                导出
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />

    </PageContainer>
  );
};

export default TableList;

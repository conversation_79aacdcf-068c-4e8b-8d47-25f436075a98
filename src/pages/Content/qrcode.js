import { DownloadOutlined, DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormItem,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space, DatePicker, InputNumber, Upload, message } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { UploadImg } from '@/util/upload';


async function getData(options) {
  // TODO: 获取用户列表
  // const data = await request('/admin/v1.0/user/list', {
  //   method: 'GET',
  //   ...(options || {}),
  // });

  return {
    data: [
      {
        id: 1,
        username: '张三',
        avatar: 'https://joeschmoe.io/api/v1/random',
      },
    ],
    success: true,
    total: 100,
  };
}

export async function addQrcode(options) {
  return request('/admin/v1.0/qrcode/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editQrcode(options) {
  return request('/admin/v1.0/qrcode/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}




const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState({});
  const [fileList, setFileList] = useState([]);
  const tableRef = useRef();
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'order_id',
    },
    {
      title: '图片',
      dataIndex: 'image',
    },
    {
      title: '类型',
      dataIndex: 'username',
      valueEnum: {
        1: { text: '客服', status: '1' },
        2: { text: '群', status: '2' },
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (text, record) => {
        return (
          <Button type="link" onClick={() => {
            // setEditVisible(true);
          }}> 编辑</Button>
        )
      },
    },

  ];

  const TYPE_OPTIONS = [
    {
      label: '客服',
      value: 0,
    },
    {
      label: '群',
      value: 1,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        actionRef={tableRef}
        request={getData}
        columns={columns}
        options={false}
        search={false}
        toolBarRender={() => [
          <Button key="export" onClick={() => {
            setAddVisible(true);
            setFileList([]);
          }}>
            新增
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
      />

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addQrcode({ ...value, image: fileList[0].url }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="名称" />
        <ProFormItem label="头像" name="avatar">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormSelect
          label="类型"
          name="type"
          options={TYPE_OPTIONS}
          placeholder="请选择类型"
        />
      </ModalForm>

      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editQrcode({ ...value, id: editData.id, image: fileList[0].url }).then(() => {
            message.success('编辑成功');
            setEditVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="名称" />
        <ProFormItem label="头像" name="avatar">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        <ProFormSelect
          label="类型"
          name="type"
          options={TYPE_OPTIONS}
          placeholder="请选择类型"
        />
      </ModalForm>

    </PageContainer>
  );
};

export default TableList;

import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormColorPicker
} from '@ant-design/pro-components';
import { Button, Dropdown, Space } from 'antd';
import { useState } from 'react';
import { request } from '@umijs/max';

async function getData(options) {
  // TODO: 获取用户列表
  // const data = await request('/admin/v1.0/user/list', {
  //   method: 'GET',
  //   ...(options || {}),
  // });

  return {
    data: [
      {
        id: 1,
        username: '张三',
        avatar: 'https://joeschmoe.io/api/v1/random',
      },
    ],
    success: true,
    total: 100,
  };
}


const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);


  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '福袋类型',
      dataIndex: 'bag_type',
      valueEnum: {
        1: { text: '福袋赏', status: '1' },
        2: { text: '聚福赏', status: '2' },
      },
    },

    {
      title: '类型代号',
      dataIndex: 'type_code',
      search: false,
    },
    {
      title: '角标展示',
      dataIndex: 'is_show',
      search: false,
      valueEnum: {
        1: { text: '是', status: '1' },
        2: { text: '否', status: '2' },
      },
    },
    {
      title: '角标文案',
      dataIndex: 'tag_text',
      sorter: true,
      search: false,
    },

    {
      title: '角标底色1',
      dataIndex: 'tag_color1',
      hideInTable: true,
    },
    {
      title: '角标底色2',
      dataIndex: 'tag_color2',
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },
          {
            type: 'divider',
          },

        ];

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  setAddVisible(true);
                }
              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}

        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => setAddVisible(true)}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
      />

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          // 新增
          console.log(value, '新增');
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormRadio.Group
          name="status"
          label="福袋类型"
          // rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '福袋赏',
              value: '1',
            },
            {
              label: '聚福赏',
              value: '2',
            },
          ]}
        />
        <ProFormText name="phone" label="类型代号" />
        <ProFormRadio.Group
          name="status"
          label="角标展示"
          // rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '是',
              value: '1',
            },
            {
              label: '否',
              value: '2',
            },
          ]}
        />

        <ProFormText name="avatar" label="角标文案" />
        <ProFormColorPicker label="角标底色1" name="color" />
        <ProFormColorPicker label="角标底色2" name="color" />
      </ModalForm>
    </PageContainer>
  );
};

export default TableList;

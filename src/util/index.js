import dayjs from 'dayjs';

export function getDateTimeStr(timestamp, formater = 'YYYY-MM-DD HH:mm:ss') {
  const timestampInt = Number(timestamp) || 0;
  if (!timestampInt) {
    return '';
  }
  const finalTimestamp = timestampInt.toString().length === 10 ? timestampInt * 1000 : timestampInt;

  return dayjs(finalTimestamp).format(formater);
}

// 将时间/时间戳转换为10位时间戳
export function dealParamsTime(time) {
  if (typeof time == 'string') {
    time = +new Date(time);
  }
  return Math.floor(time / 1000)
}
import { request } from '@umijs/max';

// 福袋相关API
export const luckyBagAPI = {
  // 获取福袋列表
  getBagList: (params: LuckyBag.BagListParams) => {
    return request<{
      data: { list: LuckyBag.BagInfo[]; total: number };
      code: number;
      message: string;
    }>('/admin/v1.0/luckybag/list', {
      method: 'POST',
      data: params,
    });
  },

  // 获取福袋详情
  getBagDetail: (params: { id: number }) => {
    return request<{
      data: LuckyBag.BagInfo;
      code: number;
      message: string;
    }>('/admin/v1.0/luckybag/get', {
      method: 'GET',
      params,
    });
  },

  // 创建福袋
  createBag: (params: Partial<LuckyBag.BagInfo>) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/create', {
      method: 'POST',
      data: params,
    });
  },

  // 更新福袋
  updateBag: (params: Partial<LuckyBag.BagInfo> & { id: number }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/update', {
      method: 'POST',
      data: params,
    });
  },

  // 更新福袋状态
  updateBagStatus: (params: { id: number; status: 0 | 1 }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/update_status', {
      method: 'POST',
      data: params,
    });
  },

  // 复制福袋
  copyBag: (params: { id: number }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/copy', {
      method: 'POST',
      data: params,
    });
  },

  // 获取用户统计
  getUserStats: (params: { bag_id: number }) => {
    return request<{
      data: Array<{
        uid: number;
        nickname: string;
        order_count: number;
        card_count: number;
      }>;
      code: number;
      message: string;
    }>('/admin/v1.0/luckybag/user_stat', {
      method: 'GET',
      params,
    });
  },
};

// 箱子相关API
export const packageAPI = {
  // 获取箱子列表
  getPackageList: (params: { bag_id: number }) => {
    return request<{
      data: { list: LuckyBag.PackageInfo[]; total: number };
      code: number;
      message: string;
    }>('/admin/v1.0/luckybag/package/list', {
      method: 'POST',
      data: params,
    });
  },

  // 创建箱子
  createPackage: (params: {
    bag_id: number;
    package_name: string;
    package_index: number;
    total_count: number;
    is_locked?: boolean;
  }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/create', {
      method: 'POST',
      data: params,
    });
  },

  // 更新箱子
  updatePackage: (params: {
    bag_package_id: number;
    package_name?: string;
    package_index?: number;
    total_count?: number;
    is_locked?: boolean;
  }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/update', {
      method: 'POST',
      data: params,
    });
  },

  // 删除箱子
  deletePackage: (params: { bag_package_id: number }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/delete', {
      method: 'POST',
      data: params,
    });
  },

  // 锁定/解锁箱子
  togglePackageLock: (params: { bag_package_id: number; is_locked: boolean }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/lock', {
      method: 'POST',
      data: params,
    });
  },
};

// 箱子卡片相关API
export const packageCardAPI = {
  // 获取箱子卡片列表
  getPackageCardList: (params: LuckyBag.PackageCardParams) => {
    return request<{
      data: { list: LuckyBag.PackageCard[]; total: number };
      code: number;
      message: string;
    }>('/admin/v1.0/luckybag/package/card/list', {
      method: 'POST',
      data: params,
    });
  },

  // 添加卡片到箱子
  addCardToPackage: (params: {
    bag_id: number;
    bag_package_id: number;
    card_id: number;
    card_num: number;
  }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/card/create', {
      method: 'POST',
      data: params,
    });
  },

  // 更新箱子中的卡片
  updatePackageCard: (params: {
    id: number;
    card_num?: number;
    probability?: number;
  }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/card/update', {
      method: 'POST',
      data: params,
    });
  },

  // 删除箱子中的卡片
  deletePackageCard: (params: { id: number }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/card/delete', {
      method: 'POST',
      data: params,
    });
  },

  // 批量设置卡片概率
  batchSetProbability: (params: {
    bag_package_id: number;
    cards: Array<{ id: number; probability: number }>;
  }) => {
    return request<{ code: number; message: string }>('/admin/v1.0/luckybag/package/card/batch_probability', {
      method: 'POST',
      data: params,
    });
  },
};

// 卡片相关API
export const cardAPI = {
  // 获取卡片列表
  getCardList: (params?: { card_ip?: number; status?: number }) => {
    return request<{
      data: { list: Array<{
        id: number;
        card_name: string;
        stock: number;
        card_sold_price: number;
        card_cost: number;
      }>; total: number };
      code: number;
      message: string;
    }>('/admin/v1.0/card/list', {
      method: 'POST',
      data: params || {},
    });
  },

  // 获取卡片IP列表
  getCardIPList: () => {
    return request<{
      data: { list: Array<{
        id: number;
        card_ip_name: string;
      }>; total: number };
      code: number;
      message: string;
    }>('/admin/v1.0/cardip/list', {
      method: 'POST',
      data: {},
    });
  },
};

import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';
import { PlusOutlined, DoubleRightOutlined, DoubleLeftOutlined, EditOutlined, MoreOutlined, BarsOutlined } from '@ant-design/icons';

import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
  ProFormSelect,
  ProFormUploadDragger,
  ProFormDigit,
  EditableProTable,
  ProCard,
  ProFormField,

} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Drawer, Input, message, Dropdown, Space, Descriptions } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { request } from '@umijs/max';
// import { editBag } from './Bag';


async function getData(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/luckybag/package/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });


  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}


async function getBagDetail(options) {
  const res = await request('/admin/v1.0/luckybag/get', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });
  return res;
}

async function add(options) {
  return request('/admin/v1.0/luckybag/package/card/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function edit(options) {
  return request('/admin/v1.0/luckybag/package/card/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function getCardList(options) {
  const res = await request('/admin/v1.0/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res.data.list;
}



const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '福袋名称',
    dataIndex: 'name',
    editable: false,
  },
  {
    title: '赏品信息',
    dataIndex: 'rewardInfo',
    width: '40%',
  },
  {
    title: '售价',
    dataIndex: 'price',
    sorter: true,
  },
  {
    title: '成本',
    dataIndex: 'acost',
    sorter: true,
  },
  {
    title: '数量',
    dataIndex: 'number',
  },
  {
    title: '已售',
    dataIndex: 'sold',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    valueType: 'dateRange',
  },
  {
    title: '更新时间',
    dataIndex: 'u_time',
    valueType: 'dateRange',
    sorter: true,
  },
  {
    title: '操作',
    valueType: 'option',
    width: 150,
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id);
        }}
      >
        编辑
      </a>,
    ],
  },
];

const BagDetail = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editableKeys, setEditableRowKeys] = useState([]);
  const [bagDetail, setBagDetail] = useState({});
  const [cardList, setCardList] = useState([]);
  const tableRef = useRef();
  const params = useParams();

  useEffect(() => {
    getBagDetail({ id: Number(params.id) }).then(res => {
      setBagDetail(res);
    });
  }, [params.id]);

  return (
    <PageContainer
      content={
        <Descriptions column={3}>
          <Descriptions.Item label="福袋单价">{bagDetail.bag_price}</Descriptions.Item>
          <Descriptions.Item label="福袋数量">{bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="福袋总价">{bagDetail.bag_price * bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="卡总售价">{bagDetail.bag_price * bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="卡总成本">{bagDetail.bag_price * bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="卡总售价:福袋总价">{bagDetail.bag_price * bagDetail.bag_count}</Descriptions.Item>
        </Descriptions>
      }
      header={{
        extra: [
          <Button key="prev" type="link" onClick={() => {
            // 上操作
          }}>
            <DoubleLeftOutlined />
            上一箱
          </Button>,
          <span>1/10</span>,
          <Button key="next" type="link" onClick={() => {
            // 下操作
          }}>
            <DoubleRightOutlined />
            下一箱
          </Button>
        ]
      }}>

      <EditableProTable
        actionRef={tableRef}
        headerTitle="xxx"
        columns={columns}
        rowKey="id"
        request={getData}
        params={{
          bag_package_id: Number(params.bag_id),
          bag_id: Number(params.id),
        }}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              key="save"
              onClick={() => {
                setAddVisible(true);
                getCardList().then(res => {
                  setCardList(res.map(item => ({
                    label: `${item.id} - ${item.card_name}(${item.stock})-(${item.card_sold_price})`,
                    value: item.id,
                  })));
                });

              }}
            >
              新增
            </Button>,
          ];
        }}

        editable={{
          // form,
          // type: 'multiple',
          editableKeys,
          showAddRow: false,
          onSave: async (_, rowInfo) => {
            return edit(rowInfo);
          },
          onChange: setEditableRowKeys,
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}

      />
      <ModalForm
        title="新增"
        layout="horizontal"
        open={addVisible}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={async (value) => {
          add({ ...value, bag_id: Number(params.id), bag_package_id: Number(params.bag_id) }).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect name="card_id" label="卡片" width="md" options={cardList} />
        <ProFormDigit
          name="card_num"
          label="数量"
          min={1}          // 最小值
          max={100}        // 最大值
          step={1}           // 步长
          defaultValue={1} // 默认值
          rules={[{ required: true, message: '请选择数量' }]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default BagDetail;

import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Dropdown, Space } from 'antd';
import { useState } from 'react';
import { request } from '@umijs/max';

async function getData(options) {
  // TODO: 获取用户列表
  // const data = await request('/admin/v1.0/user/list', {
  //   method: 'GET',
  //   ...(options || {}),
  // });

  return {
    data: [
      {
        id: 1,
        username: '张三',
        avatar: 'https://joeschmoe.io/api/v1/random',
      },
    ],
    success: true,
    total: 100,
  };
}

export async function addUser(options) {
  return request('/admin/v1.0/user/create', {
    method: 'POST',
    data: {
      method: 'post',
      ...(options || {}),
    },
  });
}

const expandedRowRender = () => {
  const data = [];
  for (let i = 0; i < 3; i += 1) {
    data.push({
      key: i,
      date: '2014-12-24 23:12:00',
      name: 'This is production name',
      upgradeNum: 'Upgraded: 56',
    });
  }
  return (
    <ProTable
      columns={[
        { title: '卡片ID', dataIndex: 'date', key: 'date' },
        { title: '卡片名称', dataIndex: 'name', key: 'name' },
        { title: '卡片数量', dataIndex: 'upgradeNum', key: 'upgradeNum' },
        { title: '获得时间', dataIndex: 'upgradeNum', key: 'upgradeNum' },
      ]}
      headerTitle="盒柜明细"
      search={false}
      options={false}
      dataSource={data}
      pagination={false}
    />
  );
};

const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [ffVisible, setFfVisible] = useState(false);
  const [kcVisible, setKcVisible] = useState(false);
  const [fjVisible, setFjVisible] = useState(false);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '昵称',
      dataIndex: 'username',
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      search: false,
      valueType: 'avatar',
      // render: (_, rowInfo) => {
      //   return <Avatar src={rowInfo.avatar} />
      // }
    },
    {
      title: '零花钱',
      dataIndex: 'linghua',
      search: false,
    },
    {
      title: '护符金币',
      dataIndex: 'jinbi',
      search: false,
    },
    {
      title: '最近下单',
      dataIndex: 'zjxd',
      sorter: true,
      search: false,
    },
    {
      title: '收入 / 纯收入 / 卡成本 / 卡数量',
      dataIndex: 'shouru',
      search: false,
      render: (_, rowInfo) => {
        return `${rowInfo.shouru} / ${rowInfo.chunshouru} / ${rowInfo.kacost} / ${rowInfo.kacount}`;
      },
    },
    {
      title: 'openid',
      dataIndex: 'openid',
      hideInTable: true,
    },
    {
      title: 'unionid',
      dataIndex: 'unionid',
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        1: { text: '正常', status: 1 },
        2: { text: '注销', status: 2 },
        3: { text: '账户封禁', status: 3 },
        4: { text: '限制消费', status: 4 },
      },
      render: (_, record) => {
        const { isReceived } = record;
        return isReceived ? '是' : '否';
      },
      // render: (text) => {
      //   return (
      //     <Paragraph ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}>{text}</Paragraph>
      //   )
      // },
      // renderFormItem: (item, config, form) => {
      //   return <ChooseStarSearch item={item} config={config} form={form} />;
      // }
    },
    {
      title: '注册时间',
      dataIndex: 'zc_time',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'c_time',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        const menuItems = [
          {
            key: '1',
            label: '编辑',
          },
          {
            type: 'divider',
          },
          {
            key: '2',
            label: '发放管理',
            disabled: true,
          },
          {
            key: '3',
            label: '扣除管理',
            disabled: true,
          },
          {
            key: '4',
            label: '用户封禁',
          },
        ];

        if (rowInfo.status === '注销') {
          menuItems.splice(3, 1);
        }

        if (rowInfo.status === '封禁/限制消费') {
          menuItems.splice(4, 1);
          menuItems.push({
            key: '5',
            label: '取消解封',
          });
        }

        return (
          <Dropdown
            menu={{
              items: menuItems,
              onClick: (item) => {
                // 编辑
                if (item.key === '1') {
                  setEditVisible(true);
                }
                // 发放管理
                if (item.key === '2') {
                  setFfVisible(true);
                }
                // 扣除管理
                if (item.key === '3') {
                  setKcVisible(true);
                }
                // 用户封禁
                if (item.key === '4') {
                  setFjVisible(true);
                }
                // 取消封禁
                if (item.key === '5') {
                  // 直接调接口
                }
              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                所有操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}
        // form={{
        //   initialValues: {
        //     payTime: defaultTime,
        //   },
        // }}
        expandable={{ expandedRowRender }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => setAddVisible(true)}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
        dateFormatter="YYYY-MM-DD HH:mm:ss"
      />
      <ModalForm
        layout="horizontal"
        open={ffVisible}
        title="发放管理"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setFfVisible(false);
          },
        }}
        onFinish={() => {
          // 发放管理
        }}
        initialValues={{}}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect
          width="md"
          options={[
            {
              value: 'chapter',
              label: '盖章后生效',
            },
          ]}
          name="chapter"
          label="选择优惠券"
          placeholder="请选择优惠券"
        />
        <ProFormDigit width="md" name="num" label="优惠券数量" placeholder="请输入优惠券数量" />
        <ProFormMoney
          width="md"
          name="money"
          label="零花钱"
          placeholder="请输入零花钱"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
        <ProFormMoney
          width="md"
          name="money"
          label="代理充值零花钱"
          placeholder="请输入代理充值零花钱"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
        <ProFormMoney
          width="md"
          name="money"
          label="护符金币"
          placeholder="请输入护符金币"
          fieldProps={{
            numberPopoverRender: true,
          }}
        />
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={fjVisible}
        title="用户封禁"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setFjVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          console.log(value, '用户封禁');
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect
          width="md"
          options={[
            {
              value: '1',
              label: '账户封禁',
            },
            {
              value: '2',
              label: '限制消费',
            },
          ]}
          name="type"
          label="封禁类型"
          placeholder="请选择封禁类型"
          rules={[{ required: true, message: '请选择封禁类型' }]}
        />

        <ProFormDatePicker name="date" label="封禁至" extra="未填时间表示永久封禁" />
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          console.log(value, '用户编辑');
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="id" label="ID" />
        <ProFormText name="uid" label="UID" />
        <ProFormText name="phone" label="手机号" />
        <ProFormText name="username" label="昵称" />
        <ProFormText name="avatar" label="头像" />
        <ProFormText name="openid" label="openid" readonly />
        <ProFormText name="unionid" label="unionid" readonly />
        <ProFormText name="device" label="设备信息" readonly />
        <ProFormRadio.Group
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '正常',
              value: '1',
            },
            {
              label: '账户封禁',
              value: '2',
            },
            {
              label: '限制消费',
              value: 'c',
            },
          ]}
        />
        <ProFormText name="c_time" label="创建时间" />
        <ProFormText name="u_time" label="更新时间" />
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          // 用户封禁
          console.log(value, '新增用户');
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="uid" label="UID" />
        <ProFormText name="phone" label="手机号" />
        <ProFormText name="username" label="昵称" />
        <ProFormText name="avatar" label="头像" />

        <ProFormRadio.Group
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '正常',
              value: '1',
            },
            {
              label: '账户封禁',
              value: '2',
            },
            {
              label: '限制消费',
              value: 'c',
            },
          ]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default TableList;

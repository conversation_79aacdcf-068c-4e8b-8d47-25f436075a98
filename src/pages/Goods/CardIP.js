import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  <PERSON>dalForm,
  PageContainer,
  ProFormDatePicker,
  ProFormDigit,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
  ProFormColorPicker,
  ProFormCheckbox, ProFormItem
} from '@ant-design/pro-components';
import { Button, message, Space, Upload } from 'antd';
import { useState, useRef } from 'react';
import { request } from '@umijs/max';
import { UploadImg } from '@/util/upload';


async function getData(options) {

  const res = await request('/admin/v1.0/cardip/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return {
    data: res.data.list,
    success: true,
    total: res.data.total,
  };
}

export async function addCardIP(options) {
  return request('/admin/v1.0/cardip/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

export async function editCardIP(options) {
  return request('/admin/v1.0/cardip/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },

  });
}


const TableList = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [editData, setEditData] = useState(null);
  const tableRef = useRef(null);
  const [fileList, setFileList] = useState([]);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      search: false,
    },
    {
      title: '卡牌IP',
      dataIndex: 'card_ip_name',
    },

    {
      title: 'IP图标',
      dataIndex: 'card_ip_image',
      search: false,
      valueType: 'image',
    },
    {
      title: '应用场景',
      dataIndex: 'scene',
      search: false,
      valueType: 'checkbox',
      valueEnum: {
        1: { text: '福袋', status: 1 },
        2: { text: '抽卡机', status: 2 },
        3: { text: '锻造', status: 3 },
      },
      sorter: true,
      search: false,
      render: (_, record) => {
        return record.scene.split(',').map(item => {
          return { 1: '福袋', 2: '抽卡机', 3: '锻造' }[item] + ',';
        }).join('').slice(0, -1)
      },
    },

    {
      title: '排序',
      dataIndex: 'sort',
      search: false,
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      search: false,
      valueEnum: {
        0: { text: '下架', status: 0 },
        1: { text: '上架', status: 1 },
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      fixed: 'right',
      search: false,
      width: 220,
      render: (_, rowInfo) => {
        return (
          <Button type="link" onClick={() => {
            setEditVisible(true)
            setEditData({ ...rowInfo, scene: rowInfo.scene.split(',').map(Number) })
            setFileList([{
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: rowInfo.card_ip_image,
            }]);
          }}>编辑</Button>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={tableRef}
        rowKey="id"
        request={getData}
        columns={columns}
        options={false}
        // params={{
        //   order_type: stateTabKey,
        // }}
        // scroll={{
        //   x: 'max-content',
        //   y: tableHeight,
        // }}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
          span: 6,
        }}

        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setAddVisible(true);
              setFileList([]);
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 20,
        }}
      />

      <ModalForm
        layout="horizontal"
        open={addVisible}
        title="新增"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={(value) => {
          addCardIP({ ...value, scene: value.scene.join(','), sort: Number(value.sort), card_ip_image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('新增成功');
              setAddVisible(false);
              tableRef.current.reload();
            }
          })
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="card_ip_name" label="卡牌IP" />
        <ProFormItem label="图片" name="card_ip_image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>

        {/* <ProFormUpload name="ip_icon" label="IP图标" /> */}
        <ProFormCheckbox.Group
          name="scene"
          label="应用场景"
          // rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '福袋',
              value: 1,
            },
            {
              label: '抽卡机',
              value: 2,
            },
            {
              label: '锻造',
              value: 3,
            },
          ]}
        />
        <ProFormText name="sort" label="排序" />
        <ProFormRadio.Group
          name="status"
          label="状态"
          // rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '上架',
              value: 1,
            },
            {
              label: '下架',
              value: 0,
            },
          ]}
        />
      </ModalForm>
      <ModalForm
        layout="horizontal"
        open={editVisible}
        title="编辑"
        initialValues={
          editData
        }
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setEditVisible(false);
          },
        }}
        onFinish={(value) => {
          editCardIP({ ...value, id: editData?.id, scene: value.scene.join(','), sort: Number(value.sort), card_ip_image: fileList[0].url }).then(res => {
            if (res.code === 0) {
              message.success('编辑成功');
              setEditVisible(false);
              tableRef.current.reload();
            }
          })
        }}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormText name="card_ip_name" label="卡牌IP" />
        <ProFormItem label="图片" name="card_ip_image">
          <Upload
            accept="image/*"
            max={1}
            listType="picture-card"
            fileList={fileList}
            onChange={({ event, file, fileList }) => {
              console.log(event, file, fileList, 'event, file, fileList');
              if (file.status === 'removed') {
                setFileList([]);
              }
              if (file.status === "uploading") {
                new UploadImg().upload(file.originFileObj).then(res => {
                  setFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: res.url,
                  }]);
                });
              }

            }}
          >
            {fileList.length >= 1 ? null : <button style={{ border: 0, background: 'none' }} type="button">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </button>
            }

          </Upload>

        </ProFormItem>
        {/* <ProFormUpload name="ip_icon" label="IP图标" /> */}
        <ProFormCheckbox.Group
          name="scene"
          label="应用场景"
          // rules={[{ required: true, message: '请选择状态' }]}
          // value={editData.scene}
          options={[
            {
              label: '福袋',
              value: 1,
            },
            {
              label: '抽卡机',
              value: 2,
            },
            {
              label: '锻造',
              value: 3,
            },
          ]}
        />
        <ProFormText name="sort" label="排序" />
        <ProFormRadio.Group
          name="status"
          label="状态"
          // rules={[{ required: true, message: '请选择状态' }]}
          options={[
            {
              label: '上架',
              value: 1,
            },
            {
              label: '下架',
              value: 0,
            },
          ]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default TableList;

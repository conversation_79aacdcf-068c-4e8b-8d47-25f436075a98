import OSS from 'ali-oss'
import { request } from '@umijs/max';

// 阿里云 OSS 配置信息（请替换为你的实际配置）
const ossConfig = {
  endpoint: "oss-cn-hangzhou.aliyuncs.com", // OSS 访问域名
  accessKeyId: "STS.NYsEDdiQCQk6B3vS4sdGjTR5z", // 访问密钥 ID
  accessKeySecret: "EsQsVeQe4JTVs9KVUVJxHSANhfarE6jVnWEZaZfKgBZg", // 访问密钥 Secret
  bucket: "mlwcw", // 存储空间名称
  region: "oss-cn-hangzhou", // 存储空间所在区域
  stsToken:
    "CAISwwJ1q6Ft5B2yfSjIr5rGDv7QhI5i5qndQBXHtzQmaMhGu5eemDz2IHhMdXBtAekavvQ2m29Y7fkZlqJ+W5ZAXUjJa8J148zZK+phjtGT1fau5Jko1beHewHKeTOZsebWZ+LmNqC/Ht6md1HDkAJq3LL+bk/Mdle5MJqP+/UFB5ZtKWveVzddA8pMLQZPsdITMWCrVcygKRn3mGHdfiEK00he8Tolt/Xgk5LNsEaF0wajlLAvyt6vcsT+Xa5FJ4xiVtq55utye5fa3TRYgxowr/8u3fUZoW6b7oHEXAYBuErdKY7T6cYqMRd9YaE2FrRUaxV0DGjLcoY5/qaAKHYlVYk9O0y3z5C+3UoeJujgsFptwFJX7+gPNkS1d/iT/UptKhkQfyBxWOEqMRQLajc3QWrxR8HqhhakK2nMRcAbGsxMj/IdpxqAAaf5t05Rds/wq2sh7oOaPKkwAS5ugHgOq1UUUxIbLxz5MI4SECvR0/YyibEwfq+KWpkdTUdCZbfKym906GuiuotZakf4GVmcEBO/X4ayCsO0lsGnkfljWGWobXjMTd2YuRcJxP4F1vwjKIm5Pes+psWd5wgwfbkohn9LtOJ2DevvIAA=", // STS 临时令牌（如果使用 STS 授权，需要填写）
};

const API = {
  getUploadToken: '/admin/v1.0/oss/get_sts_token', // 获取上传的云厂商参数
};

async function getUpToken(options) {
  // TODO: 获取用户列表
  const res = await request(API.getUploadToken, {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });

  return res;
}


export class UploadImg {
  reject = () => { }
  resolve = () => { }
  option = {}
  input = null
  client = null
  file = {}
  state = {}

  constructor(option) {
    this.option = {
      timeout: 60000,
      startUpload: () => { },
      startTransfer: () => { },
      ...option
    };
  }

  resetData = () => {
    this.resolve = () => { }
    this.reject = () => { }
    this.input && document.body.removeChild(this.input);
    this.input = null;
    this.client = null;
    this.file = {}
  }

  initialClient() {
    const { state } = this;
    console.log(state, 'state');

    this.client = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
      region: ossConfig.region,
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: state.access_key_id,
      accessKeySecret: state.access_key_secret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: state.security_token,
      // 填写Bucket名称。
      bucket: ossConfig.bucket,
      endpoint: ossConfig.endPoint,
    });
  }


  getUpToken() {
    return new Promise(async (resolve, reject) => {
      const res = await getUpToken({ stsToken: ossConfig.stsToken });
      console.log(res, 'data ------');
      if (res?.code !== 0) {
        reject({
          msg: '获取上传token失败'
        })
        return;
      }
      this.state = res.data;
      resolve(res.data);
    })
  }

  upload(file) {
    return new Promise((resolve, reject) => {
      this.resolve = (res) => {
        this.resetData();
        resolve(res);
      };
      this.reject = (err) => {
        this.resetData();
        reject(err);
      };
      if (file) {
        this.startUpload(file);
      } else {
        this.createFrom();
      }
    })
  }

  createFrom() {
    const PC = !!navigator.platform.match(/mac|win/i);
    this.input = document.createElement('input');
    this.input.type = 'file';
    this.input.accept = 'image/*';
    this.input.name = 'FileContent';
    this.input.style.cssText = 'position: fixed; height:1em; width:1em; opacity:1 ; top:-100em; left:0;';
    this.input.id = 'upload_input_id';
    document.body.appendChild(this.input);
    this.input.onchange = () => {
      this.startUpload();
    };
    this.input.click();
  }

  startUpload = async (propsfile) => {
    let file = propsfile;

    if (!file && this.input?.files) {
      file = this.input?.files[0];
    }

    if (!file) {
      this.reject && this.reject({
        msg: `请选择上传文件！`
      });
      return;
    };
    this.option.startUpload();
    this.file = file;

    this.getUpToken().then(async () => {
      let res = await this.uploadObject(file)
      this.resolve && this.resolve(res)
    }).catch(err => {
      this.reject && this.reject(err);
    })
  }

  // 简单上传
  uploadObject = async (file) => {
    const { state } = this;
    return new Promise((resolve, reject) => {
      this.initialClient()
      // 虚假进度条
      // let percent = 1
      // const interval = setInterval(() => {
      //   percent += 1
      //   onProgress && onProgress(Math.min(percent, 99))
      // }, 200)

      const fileName = `uploads/${Date.now()}_${file.name}`; // 生成唯一文件名

      this.client.put(fileName, file, { timeout: this.option.timeout }).then((res) => {
        // onProgress(100)
        // clearInterval(interval)
        console.log(res, 'res');
        resolve({
          ...res,
        })
      }).catch((err) => {
        // clearInterval(interval)
        reject(err)
      })
    })
  }

}





